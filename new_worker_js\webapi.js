// CORS 配置
const corsHeaders = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Methods': 'GET, POST, PUT, DELETE, OPTIONS',
  'Access-Control-Allow-Headers': 'Content-Type, Authorization',
  'Access-Control-Max-Age': '86400'
};

// 错误处理函数
function handleError(error) {
  console.error('API Error:', error);
  
  let status = 500;
  let message = 'Internal Server Error';
  
  if (error.message.includes('not found') || error.message.includes('No such')) {
    status = 404;
    message = 'Resource not found';
  } else if (error.message.includes('timeout') || error.message.includes('connection')) {
    status = 503;
    message = 'Database connection error';
  } else if (error.message.includes('required parameter') || error.message.includes('invalid')) {
    status = 400;
    message = error.message;
  }
  
  return new Response(JSON.stringify({ 
    error: message,
    details: error.message 
  }), {
    status: status,
    headers: { ...corsHeaders, 'Content-Type': 'application/json' }
  });
}

// 主要处理函数
export default {
  async fetch(request, env, ctx) {
    try {
      if (request.method === "OPTIONS") {
        return new Response(null, { headers: corsHeaders });
      }

      const url = new URL(request.url);
      const path = url.pathname;
      
      if (path.startsWith('/categories/tree')) {
        return await getCategoriesTree(env.DB);
      } else if (path.match(/^\/categories\/[^\/]+$/)) {
        const code = path.split('/').pop();
        return await getCategoryDetail(env.DB, code);
      }  else if (path.startsWith('/products/models/check')) { // 批量检查model或product_id是否存在
        const body = await request.json();
        const models = body.models || [];
        const product_ids = body.product_ids || [];
        return await checkModelsExistence(env.DB, models, product_ids);
      }else if (path.match(/^\/products\/[^\/]+$/)) {
        const productId = path.split('/').pop();
        return await getProductDetail(env.DB, productId, env);
      } else if (path.match(/^\/product\/image\/[^\/]+$/)) { // 新增：获取产品图片
        const imageName = path.split('/').pop();
        return await getProductImage(env["product-img"], imageName);
      } else if (path.startsWith('/products')) {
        const category = url.searchParams.get('category');
        const brand = url.searchParams.get('brand');
        const search = url.searchParams.get('search');
        const page = parseInt(url.searchParams.get('page') || '1');
        return await searchProducts(env.DB, category, brand, search, page);
      } else if (path.match(/^\/brands\/[^\/]+$/)) { // 处理 /brands/{id}
        const brandId = path.split('/').pop();
        return await getBrandDetail(env.DB, brandId);
      } else if (path.startsWith('/brands')) { // 确保这个在 /brands/{id} 之后
        const search = url.searchParams.get('search');
        const domestic = url.searchParams.get('domestic'); // 新增：获取domestic参数
        const page = parseInt(url.searchParams.get('page') || '1');
        const limit = parseInt(url.searchParams.get('limit') || '100');
        return await getBrands(env.DB, search, domestic, page, limit);
      } else if (path.startsWith('/distributors/batch')) { // 批量获取分销商
        const body = await request.json();
        const ids = body.ids || [];
        return await getDistributorsBatch(env.DB, ids);
      } else if (path.match(/^\/distributors\/image\/[^\/]+$/)) { // 新增：获取分销商图片
        const imageName = path.split('/').pop();
        return await getDistributorImage(env["distributor-img"], imageName);
      } else if (path.match(/^\/distributors\/[^\/]+$/)) { // 获取单个分销商
        const distributorId = path.split('/').pop();
        return await getDistributorDetail(env.DB, distributorId);
      } else if (path.startsWith('/distributors')) { // 搜索分销商
        const search = url.searchParams.get('search');
        const page = parseInt(url.searchParams.get('page') || '1');
        return await searchDistributors(env.DB, search, page);
      } else if (path.startsWith('/brand-distributors')) { // 品牌分销商映射
        const brandId = url.searchParams.get('brand_id');
        const distributorId = url.searchParams.get('distributor_id');
        return await getBrandDistributorMapping(env.DB, brandId, distributorId);
      }else if (path.startsWith('/product-index/first-text')) { // 获取去重的first_text列表
        return await getDistinctFirstText(env.DB2);
      } else if (path.startsWith('/product-index/by-first')) { // 根据first_text分页查询
        const firstText = url.searchParams.get('first_text');
        const page = parseInt(url.searchParams.get('page') || '1');
        return await getProductsByFirstText(env.DB2, firstText, page);
      } else if (path.startsWith('/datasheet/product')) { // 新增：根据product_id查询datasheet_url
        const productId = url.searchParams.get('product_id');
        return await getDatasheetUrl(env.DB2, productId);
      } else if (path.startsWith('/breadcrumbs')) { // 新增：面包屑生成API
        const pathname = url.searchParams.get('path') || '/';
        return await generateBreadcrumbs(env.DB, pathname);
      }

      return new Response(JSON.stringify({ error: 'Not Found' }), {
        status: 404,
        headers: { ...corsHeaders, 'Content-Type': 'application/json' }
      });
    } catch (error) {
      return handleError(error);
    }
  }
};

// API 实现函数
async function getCategoriesTree(db) {
  const { results: categories } = await db.prepare(`
    SELECT code, parent_code, level, name_cn, name_en, name_ru
    FROM categories
    ORDER BY level, code
  `).all();
  
  const buildTree = (parentCode = '') => {
    return categories
      .filter(cat => cat.parent_code === parentCode)
      .map(cat => ({
        code: cat.code,
        name_en: cat.name_en,
        name_cn: cat.name_cn,
        name_ru: cat.name_ru,
        children: buildTree(cat.code)
      }));
  };
  
  const tree = buildTree();
  
  return new Response(JSON.stringify(tree), {
    headers: { ...corsHeaders, 'Content-Type': 'application/json' }
  });
}

async function getCategoryDetail(db, code) {
  const category = await db.prepare(`
    SELECT code, parent_code, level, name_cn, name_en, name_ru
    FROM categories
    WHERE code = ?
  `).bind(code).first();
  
  if (!category) {
    return new Response(JSON.stringify({ error: 'Category not found' }), {
      status: 404,
      headers: { ...corsHeaders, 'Content-Type': 'application/json' }
    });
  }
  
  return new Response(JSON.stringify(category), {
    headers: { ...corsHeaders, 'Content-Type': 'application/json' }
  });
}

async function getProductDetail(db, productId, env) {
  // 首先查询 component_mapping 表获取 lcsc_code
  const mappingResult = await db.prepare(`
    SELECT lcsc_code FROM component_mapping WHERE ce_code = ?
  `).bind(productId).first();
  
  // 如果找到了对应的lcsc_code，则判断是否需要更新
  if (mappingResult && mappingResult.lcsc_code) {
    try {
      const lcscCode = mappingResult.lcsc_code;
      
      // 查询stocks表中对应product_id的created_at字段
      const stocksResult = await db.prepare(`
        SELECT created_at FROM stocks WHERE code = ?
      `).bind(productId).first();
      
      // 判断是否需要更新价格和库存
      let needUpdate = true;
      
      if (stocksResult && stocksResult.created_at) {
        // 解析stocks的创建/更新时间
        const stockUpdateDate = new Date(stocksResult.created_at);
        
        // 获取当前日期（年-月-日）
        const today = new Date();
        
        // 比较是否是同一天
        if (stockUpdateDate.getFullYear() === today.getFullYear() && 
            stockUpdateDate.getMonth() === today.getMonth() && 
            stockUpdateDate.getDate() === today.getDate()) {
          // 如果是同一天，则不需要更新
          console.log(`产品 ${productId} 的价格和库存信息已在今天更新过，跳过更新。`);
          needUpdate = false;
        }
      }
      
      // 只有在需要更新时才调用check-product worker
      if (needUpdate) {
        console.log(`找到lcsc_code ${lcscCode} 对应的product_id ${productId}, 正在更新产品信息...`);
        
        // 调用check-product worker更新产品信息
        const checkProductResponse = await env["check-product"].fetch(`https://check-product.harver.workers.dev?lcscCode=${lcscCode}`);
        
        if (!checkProductResponse.ok) {
          console.error(`为lcsc_code ${lcscCode}更新产品信息失败: ${checkProductResponse.status}`);
        } else {
          const updateResult = await checkProductResponse.json();
          console.log(`已更新lcsc_code ${lcscCode}的产品信息: ${JSON.stringify(updateResult)}`);
        }
      }
    } catch (error) {
      console.error(`更新产品信息时出错: ${error.message}`);
    }
  }
  
  // 继续原来的获取产品详情逻辑
  const product = await db.prepare(`
    SELECT product_id, model, brand_id, price_key, stock_key,
           datasheet_url, image_list, parameters_key, description,
           summarizer, rohs, moq, spq, weight, url
    FROM products202503
    WHERE product_id = ?
  `).bind(productId).first();

  // 如果查不到产品，但component_mapping有对应的lcsc_code，尝试调用check-product worker更新
  if (!product && mappingResult && mappingResult.lcsc_code) {
    try {
      const lcscCode = mappingResult.lcsc_code;
      console.log(`产品 ${productId} 未找到，但找到对应的lcsc_code ${lcscCode}，正在调用check-product worker更新产品信息...`);

      // 调用check-product worker更新产品信息
      const checkProductResponse = await env["check-product"].fetch(`https://check-product.harver.workers.dev?lcscCode=${lcscCode}`);

      if (!checkProductResponse.ok) {
        console.error(`为lcsc_code ${lcscCode}更新产品信息失败: ${checkProductResponse.status}`);
      } else {
        const updateResult = await checkProductResponse.json();
        console.log(`已更新lcsc_code ${lcscCode}的产品信息: ${JSON.stringify(updateResult)}`);

        // 重新查询产品信息
        const updatedProduct = await db.prepare(`
          SELECT product_id, model, brand_id, price_key, stock_key,
                 datasheet_url, image_list, parameters_key, description,
                 summarizer, rohs, moq, spq, weight, url
          FROM products202503
          WHERE product_id = ?
        `).bind(productId).first();

        if (updatedProduct) {
          // 如果更新后找到了产品，将其赋值给product变量继续处理
          console.log(`更新后成功找到产品 ${productId}`);
          product = updatedProduct;
        }
      }
    } catch (error) {
      console.error(`尝试更新产品信息时出错: ${error.message}`);
    }
  }

  // 最终检查是否找到产品
  if (!product) {
    return new Response(JSON.stringify({ error: 'Product not found' }), {
      status: 404,
      headers: { ...corsHeaders, 'Content-Type': 'application/json' }
    });
  }
  
  const brand = await db.prepare(`
    SELECT id, name_cn, name_en, name_ru, logo_url, website
    FROM brands
    WHERE id = ?
  `).bind(product.brand_id).first();
  
  const { results: prices } = await db.prepare(`
    SELECT quantity, price, created_at
    FROM price
    WHERE code = ?
    ORDER BY quantity
  `).bind(product.price_key).all();
  
  const stock = await db.prepare(`
    SELECT stocks
    FROM stocks
    WHERE code = ?
  `).bind(product.stock_key).first();
  
  const { results: parameters } = await db.prepare(`
    SELECT languages, param_name, param_value
    FROM parameters
    WHERE code = ?
  `).bind(product.parameters_key).all();
  
  const groupedParameters = {};
  parameters.forEach(param => {
    if (!groupedParameters[param.languages]) {
      groupedParameters[param.languages] = [];
    }
    groupedParameters[param.languages].push({
      param_name: param.param_name,
      param_value: param.param_value
    });
  });
  
  const categoryMap = await db.prepare(`
    SELECT category_code
    FROM product_category_map
    WHERE product_code = ?
  `).bind(productId).first();
  
  let categoryPath = [];
  let category_id = null; // 初始化第三级分类ID
  
  if (categoryMap) {
    let currentCategoryCode = categoryMap.category_code;
    let categoryHierarchy = [];
    
    while (currentCategoryCode) {
      const category = await db.prepare(`
        SELECT code, parent_code, level
        FROM categories
        WHERE code = ?
      `).bind(currentCategoryCode).first();
      
      if (!category) break;
      
      categoryHierarchy.unshift(category.code);
      
      // 如果是第三级分类，保存其ID
      if (category.level === 3) {
        category_id = category.code;
      }
      
      currentCategoryCode = category.parent_code;
    }
    
    categoryPath = categoryHierarchy;
  }
  
  // 新增：批量获取分类名称
  let category_names = {};
  if (categoryPath.length > 0) {
    // 构建查询参数，使用 IN 子句批量查询
    const placeholders = categoryPath.map(() => '?').join(',');
    const { results: categories } = await db.prepare(`
      SELECT code, name_cn, name_en, name_ru
      FROM categories
      WHERE code IN (${placeholders})
    `).bind(...categoryPath).all();
    
    // 将结果整理为以code为键的对象
    categories.forEach(cat => {
      category_names[cat.code] = {
        name_cn: cat.name_cn,
        name_en: cat.name_en,
        name_ru: cat.name_ru
      };
    });
  }
  
  // 处理 summarizer 字段，将其从文本转换为 JSON
  let summarizerJson = null;
  if (product.summarizer) {
    try {
      summarizerJson = JSON.parse(product.summarizer);
    } catch (e) {
      console.error('Error parsing summarizer JSON:', e);
      summarizerJson = null;
    }
  }
  
  const productDetail = {
    product_id: product.product_id,
    model: product.model,
    brand: brand ? {
      id: brand.id,
      name_en: brand.name_en,
      name_cn: brand.name_cn,
      name_ru: brand.name_ru,
      logo_url: brand.logo_url,
      website: brand.website
    } : null,
    prices: prices.map(p => ({
      quantity: p.quantity,
      price: p.price,
      created_at: p.created_at
    })),
    stock: stock ? stock.stocks : 0,
    parameters: groupedParameters,
    category_path: categoryPath,
    category_id: category_id, // 添加第三级分类ID
    category_names: category_names, // 添加分类名称映射
    datasheet_url: product.datasheet_url,
    image_list: product.image_list ? JSON.parse(product.image_list) : [],
    description: product.description,
    summarizer: summarizerJson, // 添加处理后的summarizer字段
    rohs: product.rohs, // 添加rohs字段
    moq: product.moq || 1, // 添加moq字段，默认为1
    spq: product.spq || 1, // 添加spq字段，默认为1
    weight: product.weight || 0, // 添加weight字段，默认为0
    url: product.url // 添加url字段
  };
  
  return new Response(JSON.stringify(productDetail), {
    headers: { ...corsHeaders, 'Content-Type': 'application/json' }
  });
}

async function searchProducts(db, category, brand, search, page) {
  const limit = 30;
  const offset = (page - 1) * limit;
  
  let conditions = [];
  let params = [];
  let keyword = ''; // 初始化keyword字段
  
  if (category) {
    conditions.push(`p.product_id IN (
      SELECT product_code 
      FROM product_category_map 
      WHERE category_code = ?
    )`);
    params.push(category);
    
    // 获取分类英文名称
    const categoryInfo = await db.prepare(`
      SELECT name_en
      FROM categories
      WHERE code = ?
    `).bind(category).first();
    
    if (categoryInfo) {
      keyword = categoryInfo.name_en;
    }
  }
  
  if (brand) {
    conditions.push('p.brand_id = ?');
    params.push(brand);
    
    // 获取品牌英文名称
    const brandInfo = await db.prepare(`
      SELECT name_en
      FROM brands
      WHERE id = ?
    `).bind(brand).first();
    
    if (brandInfo) {
      keyword = brandInfo.name_en;
    }
  }
  
  if (search) {
    conditions.push('(p.model LIKE ? OR p.product_id LIKE ?)');
    params.push(`${search}%`, `${search}%`);
    
    // 使用搜索内容作为keyword
    keyword = search;
  }
  
  const whereClause = conditions.length > 0 ? `WHERE ${conditions.join(' AND ')}` : '';
  
  // 性能优化：并行查询总数和产品列表
  const countPromise = db.prepare(`
    SELECT COUNT(*) as total
    FROM products202503 p
    ${whereClause}
  `).bind(...params).first();
  
  const productsPromise = db.prepare(`
    SELECT p.product_id, p.model, p.brand_id, p.price_key, p.stock_key,
           p.datasheet_url, p.image_list, p.parameters_key, p.description,
           p.updated_at, p.moq, p.spq, p.weight, p.url,
           b.name_cn as brand_name_cn, b.name_en as brand_name_en,
           b.name_ru as brand_name_ru
    FROM products202503 p
    LEFT JOIN brands b ON p.brand_id = b.id
    ${whereClause}
    LIMIT ? OFFSET ?
  `).bind(...params, limit, offset).all();
  
  // 并行等待结果
  const [totalCountResult, productsListResult] = await Promise.all([
    countPromise,
    productsPromise
  ]);
  
  const totalCount = totalCountResult;
  const productsList = productsListResult.results;
  
  // 如果没有产品，直接返回空结果
  if (productsList.length === 0) {
    return new Response(JSON.stringify({
      total: totalCount ? totalCount.total : 0,
      page: page,
      keyword: keyword,
      results: []
    }), {
      headers: { ...corsHeaders, 'Content-Type': 'application/json' }
    });
  }
  
  // 提取所有产品ID和需要查询的键
  const productIds = productsList.map(p => p.product_id);
  const priceKeys = [...new Set(productsList.map(p => p.price_key).filter(Boolean))];
  const stockKeys = [...new Set(productsList.map(p => p.stock_key).filter(Boolean))];
  const parameterKeys = [...new Set(productsList.map(p => p.parameters_key).filter(Boolean))];
  
  // 性能优化：批量查询所有价格、库存和分类信息
  const promises = [];
  
  // 批量查询价格
  let pricesPromise = Promise.resolve({ results: [] });
  if (priceKeys.length > 0) {
    const pricePlaceholders = priceKeys.map(() => '?').join(',');
    pricesPromise = db.prepare(`
      SELECT code, quantity, price, created_at
      FROM price
      WHERE code IN (${pricePlaceholders})
      ORDER BY quantity ASC
    `).bind(...priceKeys).all();
    promises.push(pricesPromise);
  }
  
  // 批量查询库存
  let stocksPromise = Promise.resolve({ results: [] });
  if (stockKeys.length > 0) {
    const stockPlaceholders = stockKeys.map(() => '?').join(',');
    stocksPromise = db.prepare(`
      SELECT code, stocks
      FROM stocks
      WHERE code IN (${stockPlaceholders})
    `).bind(...stockKeys).all();
    promises.push(stocksPromise);
  }
  
  // 批量查询参数信息
  let parametersPromise = Promise.resolve({ results: [] });
  if (parameterKeys.length > 0) {
    const parameterPlaceholders = parameterKeys.map(() => '?').join(',');
    parametersPromise = db.prepare(`
      SELECT code, languages, param_name, param_value
      FROM parameters
      WHERE code IN (${parameterPlaceholders})
    `).bind(...parameterKeys).all();
    promises.push(parametersPromise);
  }
  
  // 批量查询分类映射
  const categoryMapsPromise = db.prepare(`
    SELECT product_code, category_code
    FROM product_category_map
    WHERE product_code IN (${productIds.map(() => '?').join(',')})
  `).bind(...productIds).all();
  promises.push(categoryMapsPromise);
  
  // 查询所有分类数据（作为缓存使用）
  const allCategoriesPromise = db.prepare(`
    SELECT code, parent_code, level, name_cn, name_en, name_ru
    FROM categories
  `).all();
  promises.push(allCategoriesPromise);
  
  // 并行执行所有查询
  const [pricesResult, stocksResult, parametersResult, categoryMapsResult, allCategoriesResult] = 
    await Promise.all(promises);
  
  // 处理查询结果
  
  // 处理价格信息，建立price_key到价格数组的映射
  const priceMap = {};
  (pricesResult.results || []).forEach(price => {
    if (!priceMap[price.code]) {
      priceMap[price.code] = [];
    }
    priceMap[price.code].push({
      quantity: price.quantity,
      price: price.price,
      created_at: price.created_at
    });
  });
  
  // 处理库存信息，建立stock_key到库存的映射
  const stockMap = {};
  (stocksResult.results || []).forEach(stock => {
    stockMap[stock.code] = stock.stocks;
  });
  
  // 处理参数信息，建立parameters_key到参数组的映射
  const parametersMap = {};
  (parametersResult.results || []).forEach(param => {
    if (!parametersMap[param.code]) {
      parametersMap[param.code] = {};
    }
    if (!parametersMap[param.code][param.languages]) {
      parametersMap[param.code][param.languages] = [];
    }
    parametersMap[param.code][param.languages].push({
      param_name: param.param_name,
      param_value: param.param_value
    });
  });
  
  // 处理产品分类映射
  const productCategoryMap = {};
  (categoryMapsResult.results || []).forEach(map => {
    productCategoryMap[map.product_code] = map.category_code;
  });
  
  // 处理分类信息，建立高效查询缓存
  const categoryMap = {};
  const categoryNameMap = {};
  
  (allCategoriesResult.results || []).forEach(cat => {
    categoryMap[cat.code] = cat;
    categoryNameMap[cat.code] = {
      name_cn: cat.name_cn,
      name_en: cat.name_en,
      name_ru: cat.name_ru
    };
  });
  
  // 为每个产品处理分类路径和名称
  const categoryInfoMap = {};
  
  productIds.forEach(productId => {
    const initialCategoryCode = productCategoryMap[productId];
    if (!initialCategoryCode) {
      categoryInfoMap[productId] = {
        category_path: [],
        category_id: null,
        category_names: {}
      };
      return;
    }
    
    // 构建分类路径
    let currentCategoryCode = initialCategoryCode;
    const categoryPath = [];
    let categoryId = null;
    
    while (currentCategoryCode) {
      const category = categoryMap[currentCategoryCode];
      if (!category) break;
      
      // 将分类代码添加到路径的开头（从根到叶的顺序）
      categoryPath.unshift(category.code);
      
      // 如果是第三级分类，保存其ID
      if (category.level === 3) {
        categoryId = category.code;
      }
      
      currentCategoryCode = category.parent_code;
    }
    
    // 构建分类名称映射
    const categoryNames = {};
    categoryPath.forEach(code => {
      if (categoryNameMap[code]) {
        categoryNames[code] = categoryNameMap[code];
      }
    });
    
    // 存储该产品的分类信息
    categoryInfoMap[productId] = {
      category_path: categoryPath,
      category_id: categoryId,
      category_names: categoryNames
    };
  });
  
  // 组合所有信息生成最终产品列表
  const productsWithDetails = productsList.map(product => {
    // 获取该产品的价格、库存和分类信息
    const prices = priceMap[product.price_key] || [];
    const stock = product.stock_key ? (stockMap[product.stock_key] || 0) : 0;
    const parameters = product.parameters_key ? (parametersMap[product.parameters_key] || {}) : {};
    const categoryInfo = categoryInfoMap[product.product_id] || {
      category_path: [],
      category_id: null,
      category_names: {}
    };
    
    return {
      product_id: product.product_id,
      model: product.model,
      brand_id: product.brand_id,
      brand: {
        name_cn: product.brand_name_cn,
        name_en: product.brand_name_en,
        name_ru: product.brand_name_ru
      },
      price_key: product.price_key,
      stock_key: product.stock_key,
      datasheet_url: product.datasheet_url,
      image_list: product.image_list ? JSON.parse(product.image_list) : [],
      parameters_key: product.parameters_key,
      parameters: parameters, // 添加参数信息
      description: product.description,
      updated_at: product.updated_at,
      prices: prices,
      stock: stock,
      category_path: categoryInfo.category_path,
      category_id: categoryInfo.category_id,
      category_names: categoryInfo.category_names,
      moq: product.moq || 1, // 添加moq字段，默认为1
      spq: product.spq || 1, // 添加spq字段，默认为1
      weight: product.weight || 0, // 添加weight字段，默认为0
      url: product.url // 添加url字段
    };
  });
  
  const response = {
    total: totalCount ? totalCount.total : 0,
    page: page,
    keyword: keyword,
    results: productsWithDetails
  };
  
  return new Response(JSON.stringify(response), {
    headers: { ...corsHeaders, 'Content-Type': 'application/json' }
  });
}

// 新增：获取产品图片
async function getProductImage(r2Bucket_product, imageName) {
  try {
    // 从R2存储库获取图片
    const object = await r2Bucket_product.get(imageName);
    
    if (!object) {
      return new Response(JSON.stringify({ error: 'Image not found' }), {
        status: 404,
        headers: { ...corsHeaders, 'Content-Type': 'application/json' }
      });
    }
    
    // 设置适当的Content-Type
    const contentType = object.httpMetadata?.contentType || 'image/jpeg';
    
    // 返回图片数据
    return new Response(object.body, {
      headers: {
        'Content-Type': contentType,
        'Cache-Control': 'public, max-age=86400',
        'Access-Control-Allow-Origin': '*'
      }
    });
  } catch (error) {
    console.error('Error fetching image from R2:', error);
    return new Response(JSON.stringify({ error: 'Failed to fetch image' }), {
      status: 500,
      headers: { ...corsHeaders, 'Content-Type': 'application/json' }
    });
  }
}

async function getBrands(db, search, domestic, page = 1, limit = 100) {
  // 计算偏移量
  const offset = (page - 1) * limit;
  
  // 构建基础查询
  let baseQuery = `
    SELECT id, name_cn, name_en, name_ru, website, logo_url, domestic
    FROM brands
  `;
  
  // 构建计数查询
  let countQuery = `
    SELECT COUNT(*) as total
    FROM brands
  `;
  
  let params = [];
  let conditions = [];
  
  if (search) {
    conditions.push(`(name_en LIKE ? OR name_cn LIKE ? OR name_ru LIKE ?)`);
    params.push(`%${search}%`, `%${search}%`, `%${search}%`);
  }
  
  if (domestic !== null && domestic !== undefined) {
    // 如果domestic为"true"或"1"，筛选国内品牌(chinese)；如果为"false"或"0"，筛选国际品牌(international)
    const isDomestic = domestic === 'true' || domestic === '1';
    conditions.push(`domestic = ?`);
    params.push(isDomestic ? 'chinese' : 'international');
  }
  
  // 添加WHERE条件
  const whereClause = conditions.length > 0 ? ` WHERE ${conditions.join(' AND ')}` : '';
  baseQuery += whereClause;
  countQuery += whereClause;
  
  // 添加分页
  baseQuery += ` LIMIT ? OFFSET ?`;
  
  // 并行执行两个查询以提高性能
  const [brandsResult, totalCountResult] = await Promise.all([
    db.prepare(baseQuery).bind(...params, limit, offset).all(),
    db.prepare(countQuery).bind(...params).first()
  ]);
  
  // 构建响应
  const response = {
    total: totalCountResult ? totalCountResult.total : 0,
    page: page,
    limit: limit,
    results: brandsResult.results
  };
  
  return new Response(JSON.stringify(response), {
    headers: { ...corsHeaders, 'Content-Type': 'application/json' }
  });
} 

// 获取品牌详情函数
async function getBrandDetail(db, brandId) {
  const brand = await db.prepare(`
    SELECT id, introduction_en, name_cn, name_en, website, 
           name_ru, introduction_ru, logo_url, introduction_cn
    FROM brands
    WHERE id = ?
  `).bind(brandId).first();

  if (!brand) {
    return new Response(JSON.stringify({ error: 'Brand not found' }), {
      status: 404,
      headers: { ...corsHeaders, 'Content-Type': 'application/json' }
    });
  }

  return new Response(JSON.stringify(brand), {
    headers: { ...corsHeaders, 'Content-Type': 'application/json' }
  });
}

// 获取单个分销商详情
async function getDistributorDetail(db, distributorId) {
  const distributor = await db.prepare(`
    SELECT distributor_id, name_cn, name_en, name_ru, category_id, 
           description_cn, description_en, description_ru, logo, img_list, 
           contact, certification, address_cn, address_en, address_ru, website
    FROM distributors
    WHERE distributor_id = ?
  `).bind(distributorId).first();

  if (!distributor) {
    return new Response(JSON.stringify({ error: 'Distributor not found' }), {
      status: 404,
      headers: { ...corsHeaders, 'Content-Type': 'application/json' }
    });
  }

  // 处理可能的JSON字段
  if (distributor.img_list && typeof distributor.img_list === 'string') {
    try {
      distributor.img_list = JSON.parse(distributor.img_list);
    } catch (e) {
      distributor.img_list = [];
    }
  }

  if (distributor.contact && typeof distributor.contact === 'string') {
    try {
      distributor.contact = JSON.parse(distributor.contact);
    } catch (e) {
      distributor.contact = {};
    }
  }

  if (distributor.certification && typeof distributor.certification === 'string') {
    try {
      distributor.certification = JSON.parse(distributor.certification);
    } catch (e) {
      distributor.certification = [];
    }
  }

  return new Response(JSON.stringify(distributor), {
    headers: { ...corsHeaders, 'Content-Type': 'application/json' }
  });
}

// 批量获取分销商
async function getDistributorsBatch(db, ids) {
  if (!Array.isArray(ids) || ids.length === 0) {
    return new Response(JSON.stringify({ error: 'Invalid or empty distributor IDs' }), {
      status: 400,
      headers: { ...corsHeaders, 'Content-Type': 'application/json' }
    });
  }

  // 构建参数占位符
  const placeholders = ids.map(() => '?').join(',');
  
  const query = `
    SELECT distributor_id, name_cn, name_en, name_ru, category_id, 
           description_cn, description_en, description_ru, logo, img_list, 
           contact, certification, address_cn, address_en, address_ru, website
    FROM distributors
    WHERE distributor_id IN (${placeholders})
  `;
  
  const { results: distributors } = await db.prepare(query).bind(...ids).all();
  
  // 处理可能的JSON字段
  const processedDistributors = distributors.map(distributor => {
    const processed = { ...distributor };
    
    if (processed.img_list && typeof processed.img_list === 'string') {
      try {
        processed.img_list = JSON.parse(processed.img_list);
      } catch (e) {
        processed.img_list = [];
      }
    }

    if (processed.contact && typeof processed.contact === 'string') {
      try {
        processed.contact = JSON.parse(processed.contact);
      } catch (e) {
        processed.contact = {};
      }
    }

    if (processed.certification && typeof processed.certification === 'string') {
      try {
        processed.certification = JSON.parse(processed.certification);
      } catch (e) {
        processed.certification = [];
      }
    }
    
    return processed;
  });
  
  return new Response(JSON.stringify(processedDistributors), {
    headers: { ...corsHeaders, 'Content-Type': 'application/json' }
  });
}

// 搜索分销商
async function searchDistributors(db, search, page = 1) {
  const limit = 30; // 每页30个分销商
  const offset = (page - 1) * limit;
  
  let query = `
    SELECT distributor_id, name_cn, name_en, name_ru, logo, address_cn, address_en, address_ru
    FROM distributors
  `;
  
  let params = [];
  
  if (search) {
    query += ` WHERE name_cn LIKE ? OR name_en LIKE ? OR name_ru LIKE ?`;
    params = [`%${search}%`, `%${search}%`, `%${search}%`];
  }
  
  // 获取总数量
  const countQuery = `
    SELECT COUNT(*) as total
    FROM distributors
  ` + (search ? ` WHERE name_cn LIKE ? OR name_en LIKE ? OR name_ru LIKE ?` : ``);
  
  const totalCount = await db.prepare(countQuery).bind(...params).first();
  
  // 添加分页
  query += ` LIMIT ? OFFSET ?`;
  params.push(limit, offset);
  
  const { results: distributors } = await db.prepare(query).bind(...params).all();
  
  const response = {
    total: totalCount ? totalCount.total : 0,
    page: page,
    limit: limit,
    results: distributors
  };
  
  return new Response(JSON.stringify(response), {
    headers: { ...corsHeaders, 'Content-Type': 'application/json' }
  });
}

// 新增：获取分销商图片
async function getDistributorImage(r2Bucket, imageName) {
  try {
    // 从R2存储库获取图片
    const object = await r2Bucket.get(imageName);
    
    if (!object) {
      return new Response(JSON.stringify({ error: 'Image not found' }), {
        status: 404,
        headers: { ...corsHeaders, 'Content-Type': 'application/json' }
      });
    }
    
    // 设置适当的Content-Type
    const contentType = object.httpMetadata?.contentType || 'image/jpeg';
    
    // 返回图片数据
    return new Response(object.body, {
      headers: {
        'Content-Type': contentType,
        'Cache-Control': 'public, max-age=86400',
        'Access-Control-Allow-Origin': '*'
      }
    });
  } catch (error) {
    console.error('Error fetching image from R2:', error);
    return new Response(JSON.stringify({ error: 'Failed to fetch image' }), {
      status: 500,
      headers: { ...corsHeaders, 'Content-Type': 'application/json' }
    });
  }
}

// 修改：获取品牌分销商映射
async function getBrandDistributorMapping(db, brandId, distributorId) {
  // 未提供任何参数
  if (!brandId && !distributorId) {
    return new Response(JSON.stringify({ error: 'Missing required parameter: brand_id or distributor_id' }), {
      status: 400,
      headers: { ...corsHeaders, 'Content-Type': 'application/json' }
    });
  }
  
  if (brandId && !distributorId) {
    // 通过品牌ID查询分销商，并返回完整的分销商数据
    const { results: mappings } = await db.prepare(`
      SELECT distributor_id
      FROM brand_distributor_mapping
      WHERE brand_id = ?
      LIMIT 30
    `).bind(brandId).all();
    
    if (mappings.length === 0) {
      return new Response(JSON.stringify({ brand_id: brandId, distributors: [] }), {
        headers: { ...corsHeaders, 'Content-Type': 'application/json' }
      });
    }
    
    // 获取所有分销商ID
    const distributorIds = mappings.map(item => item.distributor_id);
    
    // 构建参数占位符
    const placeholders = distributorIds.map(() => '?').join(',');
    
    // 查询分销商详细信息
    const { results: distributors } = await db.prepare(`
      SELECT distributor_id, name_cn, name_en, name_ru, category_id, 
             description_cn, description_en, description_ru, logo, img_list, 
             contact, certification, address_cn, address_en, address_ru, website
      FROM distributors
      WHERE distributor_id IN (${placeholders})
    `).bind(...distributorIds).all();
    
    // 处理可能的JSON字段
    const processedDistributors = distributors.map(distributor => {
      const processed = { ...distributor };
      
      if (processed.img_list && typeof processed.img_list === 'string') {
        try {
          processed.img_list = JSON.parse(processed.img_list);
        } catch (e) {
          processed.img_list = [];
        }
      }

      if (processed.contact && typeof processed.contact === 'string') {
        try {
          processed.contact = JSON.parse(processed.contact);
        } catch (e) {
          processed.contact = {};
        }
      }

      if (processed.certification && typeof processed.certification === 'string') {
        try {
          processed.certification = JSON.parse(processed.certification);
        } catch (e) {
          processed.certification = [];
        }
      }
      
      return processed;
    });
    
    return new Response(JSON.stringify({ 
      brand_id: brandId, 
      total_count: mappings.length,
      distributors: processedDistributors 
    }), {
      headers: { ...corsHeaders, 'Content-Type': 'application/json' }
    });
  } else if (!brandId && distributorId) {
    // 通过分销商ID查询品牌，并返回完整的品牌数据
    const { results: mappings } = await db.prepare(`
      SELECT brand_id
      FROM brand_distributor_mapping
      WHERE distributor_id = ?
    `).bind(distributorId).all();
    
    if (mappings.length === 0) {
      return new Response(JSON.stringify({ distributor_id: distributorId, brands: [] }), {
        headers: { ...corsHeaders, 'Content-Type': 'application/json' }
      });
    }
    
    // 获取所有品牌ID
    const brandIds = mappings.map(item => item.brand_id);
    
    // 构建参数占位符
    const placeholders = brandIds.map(() => '?').join(',');
    
    // 查询品牌详细信息
    const { results: brands } = await db.prepare(`
      SELECT id, introduction_en, name_cn, name_en, website, 
             name_ru, introduction_ru, logo_url, introduction_cn
      FROM brands
      WHERE id IN (${placeholders})
    `).bind(...brandIds).all();
    
    return new Response(JSON.stringify({ 
      distributor_id: distributorId, 
      brands: brands 
    }), {
      headers: { ...corsHeaders, 'Content-Type': 'application/json' }
    });
  } else {
    // 同时指定品牌ID和分销商ID，验证关系并返回详细信息
    const mapping = await db.prepare(`
      SELECT brand_id, distributor_id
      FROM brand_distributor_mapping
      WHERE brand_id = ? AND distributor_id = ?
    `).bind(brandId, distributorId).first();
    
    if (!mapping) {
      return new Response(JSON.stringify({ 
        exists: false,
        message: "No mapping found between this brand and distributor" 
      }), {
        headers: { ...corsHeaders, 'Content-Type': 'application/json' }
      });
    }
    
    // 获取品牌详情
    const brand = await db.prepare(`
      SELECT id, introduction_en, name_cn, name_en, website, 
             name_ru, introduction_ru, logo_url, introduction_cn
      FROM brands
      WHERE id = ?
    `).bind(brandId).first();
    
    // 获取分销商详情
    const distributor = await db.prepare(`
      SELECT distributor_id, name_cn, name_en, name_ru, category_id, 
             description_cn, description_en, description_ru, logo, img_list, 
             contact, certification, address_cn, address_en, address_ru, website
      FROM distributors
      WHERE distributor_id = ?
    `).bind(distributorId).first();
    
    // 处理分销商的JSON字段
    if (distributor) {
      if (distributor.img_list && typeof distributor.img_list === 'string') {
        try {
          distributor.img_list = JSON.parse(distributor.img_list);
        } catch (e) {
          distributor.img_list = [];
        }
      }

      if (distributor.contact && typeof distributor.contact === 'string') {
        try {
          distributor.contact = JSON.parse(distributor.contact);
        } catch (e) {
          distributor.contact = {};
        }
      }

      if (distributor.certification && typeof distributor.certification === 'string') {
        try {
          distributor.certification = JSON.parse(distributor.certification);
        } catch (e) {
          distributor.certification = [];
        }
      }
    }
    
    return new Response(JSON.stringify({ 
      exists: true,
      brand: brand || null,
      distributor: distributor || null
    }), {
      headers: { ...corsHeaders, 'Content-Type': 'application/json' }
    });
  }
}

// 获取去重的first_text列表，按字母>数字>符号排序
async function getDistinctFirstText(db) {
  try {
    const { results } = await db.prepare(`
      SELECT DISTINCT first_text
      FROM product_index
      WHERE first_text IS NOT NULL AND first_text != ''
    `).all();
    
    if (!results || results.length === 0) {
      return new Response(JSON.stringify({
        total: 0,
        results: []
      }), {
        headers: { ...corsHeaders, 'Content-Type': 'application/json' }
      });
    }
    
    // 自定义排序函数：字母 > 数字 > 符号
    const sortFirstText = (a, b) => {
      const textA = a.first_text;
      const textB = b.first_text;
      
      // 判断字符类型的辅助函数
      const getCharType = (char) => {
        if (/[a-zA-Z]/.test(char)) return 0; // 字母
        if (/[0-9]/.test(char)) return 1;   // 数字
        return 2;                           // 符号
      };
      
      const typeA = getCharType(textA[0]);
      const typeB = getCharType(textB[0]);
      
      // 先按类型排序
      if (typeA !== typeB) {
        return typeA - typeB;
      }
      
      // 同类型按字母顺序排序
      return textA.localeCompare(textB);
    };
    
    // 排序结果
    const sortedResults = [...results].sort(sortFirstText);
    
    return new Response(JSON.stringify({
      total: sortedResults.length,
      results: sortedResults
    }), {
      headers: { ...corsHeaders, 'Content-Type': 'application/json' }
    });
  } catch (error) {
    return handleError(error);
  }
}

// 根据first_text分页查询产品索引
async function getProductsByFirstText(db, firstText, page = 1) {
  try {
    if (!firstText) {
      return new Response(JSON.stringify({
        error: 'Missing required parameter: first_text'
      }), {
        status: 400,
        headers: { ...corsHeaders, 'Content-Type': 'application/json' }
      });
    }
    
    const limit = 100; // 每页30个结果
    const offset = (page - 1) * limit;
    
    // 并行执行查询以提高性能
    const [totalCountResult, itemsResult] = await Promise.all([
      // 查询总数
      db.prepare(`
        SELECT COUNT(*) as total
        FROM product_index
        WHERE first_text = ?
      `).bind(firstText).first(),
      
      // 查询分页数据
      db.prepare(`
        SELECT id, first_text, product_id, model, url
        FROM product_index
        WHERE first_text = ?
        LIMIT ? OFFSET ?
      `).bind(firstText, limit, offset).all()
    ]);
    
    const total = totalCountResult ? totalCountResult.total : 0;
    
    return new Response(JSON.stringify({
      total: total,
      page: page,
      limit: limit,
      first_text: firstText,
      results: itemsResult.results || []
    }), {
      headers: { ...corsHeaders, 'Content-Type': 'application/json' }
    });
  } catch (error) {
    return handleError(error);
  }
}

// 批量检查产品型号或产品ID是否存在
async function checkModelsExistence(db, models, product_ids) {
  // 检查参数有效性
  if ((!Array.isArray(models) || models.length === 0) && 
      (!Array.isArray(product_ids) || product_ids.length === 0)) {
    return new Response(JSON.stringify({ 
      error: 'Invalid or empty input. Please provide models or product_ids array.' 
    }), { 
      status: 400, 
      headers: { ...corsHeaders, 'Content-Type': 'application/json' } 
    }); 
  }
  
  try {
    // 初始化结果对象
    let results = {};
    
    // 计算总项目数
    const totalModels = Array.isArray(models) ? models.length : 0;
    const totalProductIds = Array.isArray(product_ids) ? product_ids.length : 0;
    const totalItems = totalModels + totalProductIds;
    
    // 如果超过100项，进行分批处理
    const BATCH_SIZE = 100;
    
    if (totalItems > BATCH_SIZE) {
      console.log(`Processing large request with ${totalItems} items in batches of ${BATCH_SIZE}`);
      
      // 分批处理models
      if (totalModels > 0) {
        for (let i = 0; i < totalModels; i += BATCH_SIZE) {
          const modelsBatch = models.slice(i, i + BATCH_SIZE);
          const batchResults = await processModelsBatch(db, modelsBatch, []);
          results = { ...results, ...batchResults };
        }
      }
      
      // 分批处理product_ids
      if (totalProductIds > 0) {
        for (let i = 0; i < totalProductIds; i += BATCH_SIZE) {
          const productIdsBatch = product_ids.slice(i, i + BATCH_SIZE);
          const batchResults = await processModelsBatch(db, [], productIdsBatch);
          results = { ...results, ...batchResults };
        }
      }
    } else {
      // 直接处理，不分批
      results = await processModelsBatch(db, models || [], product_ids || []);
    }
    
    return new Response(JSON.stringify(results), {
      headers: { ...corsHeaders, 'Content-Type': 'application/json' }
    });
  } catch (error) {
    console.error('Error checking existence:', error);
    return new Response(JSON.stringify({ 
      error: 'Failed to check existence',
      details: error.message 
    }), { 
      status: 500, 
      headers: { ...corsHeaders, 'Content-Type': 'application/json' } 
    }); 
  }
}

// 处理一批models和product_ids的辅助函数
async function processModelsBatch(db, models, product_ids) {
  const results = {};
  
  // 处理型号查询
  if (models.length > 0) {
    // 初始化所有型号结果为不存在
    models.forEach(model => {
      results[model] = {
        exists: false,
        product_id: null,
        brand_id: null,
        brand_name_cn: null,
        brand_name_en: null,
        brand_name_ru: null,
        domestic: null,
        full: null
      };
    });
    
    // 构建参数占位符
    const modelPlaceholders = models.map(() => '?').join(',');
    
          // 批量查询产品基本信息
    const modelQuery = `
      SELECT product_id, model, brand_id, price_key, stock_key,
             datasheet_url, image_list, parameters_key, description,
             updated_at, moq, spq, weight, url
      FROM products202503
      WHERE model IN (${modelPlaceholders})
    `;
    
    const { results: modelProducts } = await db.prepare(modelQuery).bind(...models).all();
    
    // 如果找到了产品，处理这些产品
    if (modelProducts.length > 0) {
      await processProducts(db, modelProducts, results);
    }
  }
  
  // 处理产品ID查询
  if (product_ids.length > 0) {
    // 构建参数占位符
    const productIdPlaceholders = product_ids.map(() => '?').join(',');
    
    // 批量查询产品基本信息
    const productIdQuery = `
      SELECT product_id, model, brand_id, price_key, stock_key,
             datasheet_url, image_list, parameters_key, description,
             updated_at, moq, spq, weight, url
      FROM products202503
      WHERE product_id IN (${productIdPlaceholders})
    `;
    
    const { results: idProducts } = await db.prepare(productIdQuery).bind(...product_ids).all();
    
    // 初始化model结果为不存在
    idProducts.forEach(product => {
      results[product.model] = {
        exists: false,
        product_id: null,
        brand_id: null,
        brand_name_cn: null,
        brand_name_en: null,
        brand_name_ru: null,
        domestic: null,
        full: null
      };
    });
    
    // 如果找到了产品，处理这些产品
    if (idProducts.length > 0) {
      await processProducts(db, idProducts, results);
    }
  }
  
  return results;
}

// 处理产品列表的辅助函数
// 新增：根据product_id查询datasheet_url
async function getDatasheetUrl(db, productId) {
  // 验证参数
  if (!productId) {
    return new Response(JSON.stringify({
      error: 'Missing required parameter: product_id'
    }), {
      status: 400,
      headers: { ...corsHeaders, 'Content-Type': 'application/json' }
    });
  }

  try {
    // 查询datasheet_url数据表
    const result = await db.prepare(`
      SELECT product_id, datasheet_url
      FROM datasheet_url
      WHERE product_id = ?
    `).bind(productId).first();

    if (!result) {
      return new Response(JSON.stringify({
        product_id: productId,
        exists: false,
        message: 'No datasheet URL found for this product ID'
      }), {
        status: 404,
        headers: { ...corsHeaders, 'Content-Type': 'application/json' }
      });
    }

    // 返回结果
    return new Response(JSON.stringify({
      product_id: result.product_id,
      exists: true,
      datasheet_url: result.datasheet_url
    }), {
      headers: { ...corsHeaders, 'Content-Type': 'application/json' }
    });
  } catch (error) {
    console.error('Error fetching datasheet URL:', error);
    return handleError(error);
  }
}

async function processProducts(db, products, results) {
  // 获取所有相关的品牌ID、价格键、库存键和产品ID
  const brandIds = [...new Set(products.map(p => p.brand_id).filter(id => id))];
  const priceKeys = [...new Set(products.map(p => p.price_key).filter(key => key))];
  const stockKeys = [...new Set(products.map(p => p.stock_key).filter(key => key))];
  const productIds = products.map(p => p.product_id);
  const parameterKeys = [...new Set(products.map(p => p.parameters_key).filter(key => key))];
  
  // 并行执行所有批量查询
  const promises = [];
  
  // 批量查询品牌信息
  let brandsPromise = Promise.resolve({ results: [] });
  if (brandIds.length > 0) {
    const brandPlaceholders = brandIds.map(() => '?').join(',');
    brandsPromise = db.prepare(`
      SELECT id, name_cn, name_en, name_ru, logo_url, website, domestic
      FROM brands
      WHERE id IN (${brandPlaceholders})
    `).bind(...brandIds).all();
    promises.push(brandsPromise);
  }
  
  // 批量查询价格信息
  let pricesPromise = Promise.resolve({ results: [] });
  if (priceKeys.length > 0) {
    const pricePlaceholders = priceKeys.map(() => '?').join(',');
    pricesPromise = db.prepare(`
      SELECT code, quantity, price, created_at
      FROM price
      WHERE code IN (${pricePlaceholders})
      ORDER BY quantity ASC
    `).bind(...priceKeys).all();
    promises.push(pricesPromise);
  }
  
  // 批量查询库存信息
  let stocksPromise = Promise.resolve({ results: [] });
  if (stockKeys.length > 0) {
    const stockPlaceholders = stockKeys.map(() => '?').join(',');
    stocksPromise = db.prepare(`
      SELECT code, stocks
      FROM stocks
      WHERE code IN (${stockPlaceholders})
    `).bind(...stockKeys).all();
    promises.push(stocksPromise);
  }
  
  // 批量查询参数信息
  let parametersPromise = Promise.resolve({ results: [] });
  if (parameterKeys.length > 0) {
    const parameterPlaceholders = parameterKeys.map(() => '?').join(',');
    parametersPromise = db.prepare(`
      SELECT code, languages, param_name, param_value
      FROM parameters
      WHERE code IN (${parameterPlaceholders})
    `).bind(...parameterKeys).all();
    promises.push(parametersPromise);
  }
  
  // 批量查询产品分类映射
  let categoryMapsPromise = Promise.resolve({ results: [] });
  if (productIds.length > 0) {
    const productIdPlaceholders = productIds.map(() => '?').join(',');
    categoryMapsPromise = db.prepare(`
      SELECT product_code, category_code
      FROM product_category_map
      WHERE product_code IN (${productIdPlaceholders})
    `).bind(...productIds).all();
    promises.push(categoryMapsPromise);
  }
  
  // 查询所有分类数据（作为缓存使用）
  const allCategoriesPromise = db.prepare(`
    SELECT code, parent_code, level, name_cn, name_en, name_ru
    FROM categories
  `).all();
  promises.push(allCategoriesPromise);
  
  // 并行等待所有查询结果
  const [brandsResult, pricesResult, stocksResult, parametersResult, categoryMapsResult, allCategoriesResult] = 
    await Promise.all(promises);
  
  // 处理品牌信息，建立brand_id到品牌对象的映射
  const brandMap = {};
  (brandsResult.results || []).forEach(brand => {
    brandMap[brand.id] = brand;
  });
  
  // 处理价格信息，建立price_key到价格数组的映射
  const priceMap = {};
  (pricesResult.results || []).forEach(price => {
    if (!priceMap[price.code]) {
      priceMap[price.code] = [];
    }
    priceMap[price.code].push({
      quantity: price.quantity,
      price: price.price,
      created_at: price.created_at
    });
  });
  
  // 处理库存信息，建立stock_key到库存的映射
  const stockMap = {};
  (stocksResult.results || []).forEach(stock => {
    stockMap[stock.code] = stock.stocks;
  });
  
  // 处理参数信息，建立parameters_key到参数组的映射
  const parametersMap = {};
  (parametersResult.results || []).forEach(param => {
    if (!parametersMap[param.code]) {
      parametersMap[param.code] = {};
    }
    if (!parametersMap[param.code][param.languages]) {
      parametersMap[param.code][param.languages] = [];
    }
    parametersMap[param.code][param.languages].push({
      param_name: param.param_name,
      param_value: param.param_value
    });
  });
  
  // 处理分类映射，建立product_id到category_code的映射
  const productCategoryMap = {};
  (categoryMapsResult.results || []).forEach(map => {
    productCategoryMap[map.product_code] = map.category_code;
  });
  
  // 处理分类数据，建立高效查询缓存
  const categoryMap = {};
  const categoryNameMap = {};
  
  (allCategoriesResult.results || []).forEach(cat => {
    categoryMap[cat.code] = cat;
    categoryNameMap[cat.code] = {
      name_cn: cat.name_cn,
      name_en: cat.name_en,
      name_ru: cat.name_ru
    };
  });
  
  // 为每个产品处理分类路径和ID
  const categoryInfoMap = {};
  
  productIds.forEach(productId => {
    const initialCategoryCode = productCategoryMap[productId];
    if (!initialCategoryCode) {
      categoryInfoMap[productId] = {
        category_path: [],
        category_id: null,
        category_names: {}
      };
      return;
    }
    
    // 构建分类路径
    let currentCategoryCode = initialCategoryCode;
    const categoryPath = [];
    let categoryId = null;
    
    while (currentCategoryCode) {
      const category = categoryMap[currentCategoryCode];
      if (!category) break;
      
      // 将分类代码添加到路径的开头（从根到叶的顺序）
      categoryPath.unshift(category.code);
      
      // 如果是第三级分类，保存其ID
      if (category.level === 3) {
        categoryId = category.code;
      }
      
      currentCategoryCode = category.parent_code;
    }
    
    // 构建分类名称映射
    const categoryNames = {};
    categoryPath.forEach(code => {
      if (categoryNameMap[code]) {
        categoryNames[code] = categoryNameMap[code];
      }
    });
    
    // 存储该产品的分类信息
    categoryInfoMap[productId] = {
      category_path: categoryPath,
      category_id: categoryId,
      category_names: categoryNames
    };
  });
  
  // 更新结果
  products.forEach(product => {
    const model = product.model;
    const brand = brandMap[product.brand_id] || null;
    const prices = priceMap[product.price_key] || [];
    const stock = product.stock_key ? (stockMap[product.stock_key] || 0) : 0;
    const parameters = product.parameters_key ? (parametersMap[product.parameters_key] || {}) : {};
    const categoryInfo = categoryInfoMap[product.product_id] || {
      category_path: [],
      category_id: null,
      category_names: {}
    };
    
    results[model] = {
      exists: true,
      product_id: product.product_id,
      brand_id: product.brand_id,
      brand_name_cn: brand ? brand.name_cn : null,
      brand_name_en: brand ? brand.name_en : null,
      brand_name_ru: brand ? brand.name_ru : null,
      domestic: brand ? brand.domestic : null,
      full: {
        product_id: product.product_id,
        model: product.model,
        brand_id: product.brand_id,
        brand: brand ? {
          name_cn: brand.name_cn,
          name_en: brand.name_en,
          name_ru: brand.name_ru,
          logo_url: brand.logo_url,
          website: brand.website
        } : null,
        price_key: product.price_key,
        stock_key: product.stock_key,
        datasheet_url: product.datasheet_url,
        image_list: product.image_list ? JSON.parse(product.image_list) : [],
        parameters_key: product.parameters_key,
        parameters: parameters, // 添加参数信息
        description: product.description,
        updated_at: product.updated_at,
        prices: prices,
        stock: stock,
        category_path: categoryInfo.category_path,
        category_id: categoryInfo.category_id,
        category_names: categoryInfo.category_names,
        moq: product.moq || 1, // 添加moq字段，默认为1
        spq: product.spq || 1, // 添加spq字段，默认为1
        weight: product.weight || 0, // 添加weight字段，默认为0
        url: product.url // 添加url字段
      }
    };
  });
}

// 面包屑生成相关函数

// 从新URL格式中提取产品ID
function extractProductIdFromUrl(urlPath) {
  // 针对CMA格式产品ID的精确匹配
  const cmaMatch = urlPath.match(/CMA\d{9,12}/);
  if (cmaMatch) {
    return cmaMatch[0];
  }

  // 通过末尾是否有CMA+数字来识别产品ID
  if (urlPath.includes('-CMA')) {
    const parts = urlPath.split('-');
    for (let i = parts.length - 1; i >= 0; i--) {
      if (parts[i].startsWith('CMA') && /CMA\d+/.test(parts[i])) {
        return parts[i];
      }
    }
  }

  // 新URL格式: {一级分类}-{二级分类}-{三级分类}-{品牌名称(可能包含横杠)}-{model(可能包含横杠)}-{产品id}
  // 提取最后一个部分作为产品ID
  const parts = urlPath.split('-');

  // 检查最后一部分是否是产品ID (CMA格式或纯数字)
  const lastPart = parts[parts.length - 1];
  if (lastPart && (lastPart.startsWith('CMA') || /^\d+$/.test(lastPart))) {
    return lastPart;
  }

  // 如果我们有足够的部分，假设最后一个是产品ID
  if (parts.length >= 6) {
    return parts[parts.length - 1];
  }

  // 如果无法按新格式解析，返回原始路径
  return urlPath;
}

// 获取分类名称
async function fetchCategoryName(db, categoryId) {
  try {
    if (!categoryId) return '';

    const category = await db.prepare(`
      SELECT name_en
      FROM categories
      WHERE code = ?
    `).bind(categoryId).first();

    return category ? category.name_en : '';
  } catch (error) {
    console.error('Error fetching category data:', error);
    return '';
  }
}

// 获取实体名称
async function fetchEntityName(db, type, id) {
  try {
    let query = '';
    let nameField = '';

    if (type === 'products') {
      query = `
        SELECT model, brand_id, product_id
        FROM products202503
        WHERE product_id = ?
      `;

      const product = await db.prepare(query).bind(id).first();
      if (!product) return { name: id };

      // 获取分类信息
      const categoryMap = await db.prepare(`
        SELECT category_code
        FROM product_category_map
        WHERE product_code = ?
      `).bind(id).first();

      let categoryId = null;
      if (categoryMap) {
        // 找到第三级分类
        let currentCategoryCode = categoryMap.category_code;
        while (currentCategoryCode) {
          const category = await db.prepare(`
            SELECT code, parent_code, level
            FROM categories
            WHERE code = ?
          `).bind(currentCategoryCode).first();

          if (!category) break;

          if (category.level === 3) {
            categoryId = category.code;
            break;
          }

          currentCategoryCode = category.parent_code;
        }
      }

      return {
        name: product.model || id,
        categoryData: {
          path: '',
          id: categoryId || ''
        }
      };
    } else if (type === 'brands') {
      query = `
        SELECT name_en
        FROM brands
        WHERE id = ?
      `;
      nameField = 'name_en';
    } else if (type === 'distributors') {
      query = `
        SELECT name_en
        FROM distributors
        WHERE distributor_id = ?
      `;
      nameField = 'name_en';
    }

    if (query && nameField) {
      const result = await db.prepare(query).bind(id).first();
      return { name: result ? result[nameField] : id };
    }

    return { name: id };
  } catch (error) {
    console.error(`Error fetching ${type} data:`, error);
    return { name: id };
  }
}

// 获取专题信息 (模拟函数，实际需要连接到blog-manage API)
async function fetchInsightTopic(id) {
  try {
    // 这里应该调用实际的API，暂时返回ID
    return id;
  } catch (error) {
    console.error('Error fetching insight data:', error);
    return id;
  }
}

// 获取文章信息 (模拟函数，实际需要连接到blog-manage API)
async function fetchBlogArticle(id) {
  try {
    // 这里应该调用实际的API，暂时返回ID
    return id;
  } catch (error) {
    console.error('Error fetching article data:', error);
    return id;
  }
}

// 获取Spotlight信息 (模拟函数，实际需要连接到blog-manage API)
async function fetchSpotlight(id) {
  try {
    // 这里应该调用实际的API，暂时返回ID
    return id;
  } catch (error) {
    console.error('Error fetching spotlight data:', error);
    return id;
  }
}

// 生成面包屑主函数
async function generateBreadcrumbsData(db, pathname) {
  // 处理完整URL，提取路径部分
  let processedPath = pathname;

  // 如果传入的是完整URL，提取路径部分
  if (pathname.startsWith('http://') || pathname.startsWith('https://')) {
    try {
      const url = new URL(pathname);
      processedPath = url.pathname + (url.search || '');
    } catch (error) {
      console.error('Error parsing URL:', error);
      // 如果URL解析失败，尝试手动提取路径
      const match = pathname.match(/^https?:\/\/[^\/]+(.*)$/);
      if (match) {
        processedPath = match[1] || '/';
      }
    }
  }

  // 分离路径和查询参数
  const [pathOnly, queryString] = processedPath.split('?');
  const paths = pathOnly.split('/').filter(path => path);
  const items = [];
  let currentPath = '';

  // Always add home as first item
  items.push({ label: 'Home', path: '/' });

  // 处理搜索页面
  if (paths.length >= 1 && paths[0] === 'search') {
    // 添加Search作为第二级
    items.push({
      label: 'Search',
      path: '/search'
    });

    return items;
  }

  // 处理product_index页面
  if (paths.length >= 1 && paths[0] === 'product_index') {
    // 添加Product Index作为第二级，但链接指向/product_index/A
    items.push({
      label: 'Product Index',
      path: '/product_index/A'
    });

    // 如果是具体的首字母页面，添加首字母作为最后一级
    if (paths.length >= 2) {
      const firstText = paths[1];

      // 添加首字母作为最后一级
      items.push({
        label: `"${firstText}"`,
        path: processedPath
      });
    }

    return items;
  }

  // 处理insights专题和文章页面
  if (paths.length >= 1 && paths[0] === 'insights') {
    // 添加Insights作为第二级
    items.push({
      label: 'Insights',
      path: '/insights'
    });

    // 如果只是/insights路径，不再添加额外的面包屑
    if (paths.length === 1) {
      return items;
    }

    // 处理专题详情页
    if (paths.length >= 2) {
      const topicId = paths[1];
      const topicName = await fetchInsightTopic(topicId);

      // 添加专题名称
      const topicPath = `/insights/${topicId}`;
      items.push({
        label: topicName,
        path: topicPath
      });

      // 如果是文章详情页，还需要添加文章标题
      if (paths.length >= 3) {
        const articleId = paths[2];
        const articleTitle = await fetchBlogArticle(articleId);

        // 添加文章标题
        items.push({
          label: articleTitle,
          path: processedPath
        });
      }

      return items;
    }
  }

  // 处理spotlight页面
  if (paths.length >= 1 && paths[0] === 'spotlight') {
    // 添加Spotlight作为第二级
    items.push({
      label: 'Spotlight',
      path: '/spotlight'
    });

    // 如果只是/spotlight路径，不再添加额外的面包屑
    if (paths.length === 1) {
      return items;
    }

    // 处理spotlight详情页
    if (paths.length >= 2) {
      const spotlightId = paths[1];
      const spotlightTitle = await fetchSpotlight(spotlightId);

      // 添加spotlight标题
      items.push({
        label: spotlightTitle,
        path: processedPath
      });

      return items;
    }
  }

  // 处理产品详情页的特殊情况 - 适配新的URL格式
  if (paths.length >= 2 && paths[0] === 'product') {
    const productPath = paths[1];

    // 提取产品ID，无论是新格式还是旧格式
    const productId = extractProductIdFromUrl(productPath);

    // 通过API获取产品信息和分类信息
    const { name, categoryData } = await fetchEntityName(db, 'products', productId);

    // 添加分类作为第二级面包屑（如果有）
    if (categoryData && categoryData.id) {
      // 获取分类名称
      const categoryName = await fetchCategoryName(db, categoryData.id);

      if (categoryName) {
        items.push({
          label: categoryName,
          path: `/search?category=${categoryData.id}`
        });
      }
    }

    // 添加产品型号作为最后一级
    items.push({
      label: name,
      path: processedPath
    });

    return items;
  }

  // 处理其他页面
  for (let i = 0; i < paths.length; i++) {
    const path = paths[i];
    currentPath += `/${path}`;

    let label = path;

    // 处理特殊路径
    if (i === 0) {
      // 处理第一级路径
      if (path === 'product') {
        label = 'Product';
      } else {
        label = path.charAt(0).toUpperCase() + path.slice(1);
      }
    } else {
      // 处理第二级及以后的路径
      const prevPath = paths[i - 1].toLowerCase();

      if (prevPath === 'manufacturers') {
        const { name } = await fetchEntityName(db, 'brands', path);
        label = name;
      } else if (prevPath === 'distributors') {
        const { name } = await fetchEntityName(db, 'distributors', path);
        label = name;
      } else if (prevPath === 'product') {
        // 对于新的产品URL格式，使用提取的产品ID
        const productId = extractProductIdFromUrl(path);
        const { name } = await fetchEntityName(db, 'products', productId);
        label = name;
      } else {
        // 默认处理：将连字符转换为空格并首字母大写
        label = path
          .split('-')
          .map(word => word.charAt(0).toUpperCase() + word.slice(1))
          .join(' ');
      }
    }

    items.push({ label, path: currentPath });
  }

  return items;
}

// API 路由处理函数
async function generateBreadcrumbs(db, pathname) {
  try {
    // 生成面包屑
    const breadcrumbs = await generateBreadcrumbsData(db, pathname);

    // 返回数据
    return new Response(JSON.stringify({
      breadcrumbs,
      path: pathname
    }), {
      headers: { ...corsHeaders, 'Content-Type': 'application/json' }
    });
  } catch (error) {
    console.error('Error generating breadcrumbs:', error);
    return new Response(JSON.stringify({
      error: 'Failed to generate breadcrumbs',
      details: error.message
    }), {
      status: 500,
      headers: { ...corsHeaders, 'Content-Type': 'application/json' }
    });
  }
}