/**
 * <PERSON>flare Worker脚本 - datasheet下载器
 * 功能：接收product_id参数，从D1数据库获取datasheet_url并提供下载或预览
 */

export default {
  async fetch(request, env) {
    try {
      // 获取URL参数
      const url = new URL(request.url);
      const productId = url.searchParams.get('product_id');
      // 添加mode参数，可以是"download"或"view"，默认为"view"
      const mode = url.searchParams.get('mode') || 'view';

      // 检查是否提供了product_id
      if (!productId) {
        return new Response('缺少必要参数: product_id', { 
          status: 400,
          headers: { 'Content-Type': 'text/plain;charset=UTF-8' }
        });
      }

      // 从D1数据库中查询datasheet_url
      const { results } = await env.DB.prepare(
        'SELECT datasheet_url FROM products202503 WHERE product_id = ?'
      ).bind(productId).all();

      // 检查是否找到记录
      if (!results || results.length === 0) {
        return new Response(`未找到ID为 ${productId} 的产品数据`, { 
          status: 404,
          headers: { 'Content-Type': 'text/plain;charset=UTF-8' }
        });
      }

      const datasheetUrl = results[0].datasheet_url;

      // 检查datasheet_url是否存在
      if (!datasheetUrl) {
        return new Response(`ID为 ${productId} 的产品没有关联的datasheet`, { 
          status: 404,
          headers: { 'Content-Type': 'text/plain;charset=UTF-8' }
        });
      }

      console.log(`尝试获取datasheet: ${datasheetUrl}, 模式: ${mode}`);

      // 获取原始请求的头信息用于构建新请求
      const requestHeaders = new Headers();
      
      // 伪装成浏览器请求
      requestHeaders.set('User-Agent', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/132.0.0.0 Safari/537.36');
      requestHeaders.set('Accept', 'text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7');
      requestHeaders.set('Accept-Language', 'zh-CN,zh;q=0.9,en-US;q=0.8,en;q=0.7');
      requestHeaders.set('Cache-Control', 'no-cache');
      
      // 设置引用来源为原始URL的域名
      try {
        const urlObject = new URL(datasheetUrl);
        requestHeaders.set('Referer', `${urlObject.protocol}//${urlObject.hostname}/`);
      } catch (error) {
        console.error('设置Referer失败:', error);
      }

      // 获取远程datasheet文件
      const response = await fetch(datasheetUrl, {
        headers: requestHeaders,
        redirect: 'follow'  // 跟随重定向
      });
      
      if (!response.ok) {
        console.error(`获取文件失败: ${response.status} ${response.statusText}`);
        return new Response(`无法获取datasheet文件 (状态码: ${response.status})`, { 
          status: response.status,
          headers: { 'Content-Type': 'text/plain;charset=UTF-8' }
        });
      }

      // 获取文件内容
      const fileContent = await response.arrayBuffer();
      
      // 从URL中提取文件名
      let fileName = 'datasheet.pdf';
      try {
        const urlPath = new URL(datasheetUrl).pathname;
        const pathSegments = urlPath.split('/');
        if (pathSegments.length > 0 && pathSegments[pathSegments.length - 1]) {
          fileName = pathSegments[pathSegments.length - 1];
        }
      } catch (error) {
        console.error('提取文件名失败:', error);
      }

      // 设置响应头
      const headers = new Headers();
      const contentType = response.headers.get('Content-Type') || 'application/pdf';
      headers.set('Content-Type', contentType);
      
      // 根据模式设置不同的响应头
      if (mode === 'download') {
        // 下载模式 - 添加Content-Disposition头，强制浏览器下载
        headers.set('Content-Disposition', `attachment; filename="${fileName}"`);
      } else {
        // 查看模式 - 使用inline，浏览器将尝试在线显示
        headers.set('Content-Disposition', `inline; filename="${fileName}"`);
      }
      
      // 缓存控制
      headers.set('Cache-Control', 'public, max-age=86400'); // 缓存一天

      console.log(`成功获取文件: ${fileName}, 大小: ${fileContent.byteLength} 字节, 模式: ${mode}`);

      // 返回文件内容
      return new Response(fileContent, {
        status: 200,
        headers
      });

    } catch (error) {
      console.error('处理请求时出错:', error);
      return new Response('服务器处理请求时出错: ' + error.message, { 
        status: 500,
        headers: { 'Content-Type': 'text/plain;charset=UTF-8' }
      });
    }
  }
}; 