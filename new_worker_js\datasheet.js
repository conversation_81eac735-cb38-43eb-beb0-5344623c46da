// 配置允许的域名列表
const ALLOWED_ORIGINS = [
    'https://testpage.962692556.workers.dev',
    'https://data-workers.962692556.workers.dev',
    'https://dash.cloudflare.com',
    'https://auth.962692556.workers.dev',
  ];
  
export default {
    async fetch(request, env, ctx) {
        const origin = request.headers.get('Origin');
        console.log('Datasheet fetch - Request origin:', origin);

        // 更新 CORS 配置
        const corsHeaders = {
            'Access-Control-Allow-Origin': origin || '*',
            'Access-Control-Allow-Methods': 'GET, OPTIONS',
            'Access-Control-Allow-Headers': 'Content-Type, Authorization',
            'Access-Control-Max-Age': '86400',
        };

        // 处理预检请求
        if (request.method === 'OPTIONS') {
            return new Response(null, { 
                headers: corsHeaders,
                status: 200
            });
        }

        try {
            // 获取请求的URL路径并进行解码
            const url = new URL(request.url);
            let path = decodeURIComponent(url.pathname.replace(/^\//, ''));

            // 如果URL包含字符串"962692556"，就将"962692556"替换为"harver"
            if (path.includes('962692556')) {
                path = path.replace(/962692556/g, 'harver');
                console.log('URL path modified - replaced 962692556 with harver:', path);
            }

            // 检查 R2 存储桶是否可用
            if (!env.DATASHEET_BUCKET) {
                console.error('R2 bucket not found');
                return new Response(JSON.stringify({
                    success: false,
                    error: 'Storage configuration error'
                }), {
                    status: 500,
                    headers: {
                        'Content-Type': 'application/json',
                        ...corsHeaders
                    }
                });
            }

            // 如果路径为空，直接返回空内容
            if (!path) {
                return new Response(JSON.stringify({
                    success: true,
                    message: 'Root directory access'
                }), {
                    headers: {
                        'Content-Type': 'application/json',
                        ...corsHeaders
                    }
                });
            }

            // 获取特定文件
            console.log('Getting object:', path);  // 添加日志
            const object = await env.DATASHEET_BUCKET.get(path);

            // 如果文件不存在
            if (!object) {
                return new Response(JSON.stringify({
                    success: false,
                    error: `File not found: ${path}`
                }), {
                    status: 404,
                    headers: {
                        'Content-Type': 'application/json',
                        ...corsHeaders
                    }
                });
            }

            // 获取文件的元数据
            const headers = new Headers(corsHeaders);
            object.writeHttpMetadata(headers);
            headers.set('etag', object.httpEtag);

            // 对于 PDF 文件设置正确的 Content-Type
            if (path.toLowerCase().endsWith('.pdf')) {
                headers.set('Content-Type', 'application/pdf');
            }

            // 返回文件内容
            return new Response(object.body, { headers });

        } catch (error) {
            console.error('Datasheet fetch - Error:', error);
            return new Response(JSON.stringify({
                success: false,
                error: error.message
            }), {
                status: 500,
                headers: {
                    'Content-Type': 'application/json',
                    ...corsHeaders
                }
            });
        }
    }
};